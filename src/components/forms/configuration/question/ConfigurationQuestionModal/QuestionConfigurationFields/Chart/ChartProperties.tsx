import React, { use<PERSON><PERSON>back, useContext, useMemo, useState } from "react";
import { useOutletContext } from "react-router-dom";

import { reorderWithEdge } from "@atlaskit/pragmatic-drag-and-drop-hitbox/util/reorder-with-edge";
import { Doc, Prop } from "@automerge/automerge-repo";
import {
  Accordion,
  ChartType,
  ColorText,
  CustomAccordionTrigger,
  DropdownItem,
  Heading,
  HeadingSize,
  Inline,
  MultiSelect,
  OnDrop,
  OpenCloseIcon,
  Pill,
  RadioGroup,
  SplitButton,
  Stack
} from "@oneteam/onetheme";

import {
  createChartQuestion,
  getByPath
} from "@helpers/configurationFormHelper.ts";

import { QuestionType } from "@components/forms/QuestionType/QuestionType";
import { ConfigurationFormAddLine } from "@components/forms/configuration/ConfigurationFormAddLine/ConfigurationFormAddLine";
import { LabelledDivider } from "@components/shared/LabelledDivider";

import { ConfigurationFormContext } from "@pages/configuration/forms/ConfigurationFormContext";
import { DocChange } from "@pages/workspace/WorkspaceLayout.tsx";

import { useCheckQuestionDuplicateIdentifier } from "@src/hooks/uniqueIdentifier/useCheckQuestionDuplicateIdentifier";
import { Dictionary } from "@src/hooks/useDictionary.tsx";
import { FormPath } from "@src/types/Form";
import { ConfigurationFormMode } from "@src/types/FormConfiguration";
import {
  ChartQuestionTypes,
  MultiLevelQuestion,
  Question
} from "@src/types/Question.ts";
import {
  TableDefaultView,
  TableQuestionProperties
} from "@src/types/QuestionProperties";
import { WorkspaceDocument } from "@src/types/documentTypes.ts";

import { ChartQuestions } from "./ChartQuestions";

export const ChartProperties = ({
  question,
  path,
  d,
  isExpanded = true,
  showReorder,
  onChangeExpanded,
  mode,
  showAddChartButton = false
}: {
  question: Question<TableQuestionProperties>;
  path: Prop[];
  d: Dictionary;
  showReorder?: boolean;
  isExpanded?: boolean;
  onChangeExpanded?: (expanded: boolean) => void;
  mode: `${ConfigurationFormMode}`;
  showAddChartButton?: boolean;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const onClickWithClose = (callback: () => void) => () => {
    setIsOpen(false);
    callback();
  };
  const childQuestionsAccessor = "charts";

  const { docChange } = useOutletContext<{
    document: Doc<WorkspaceDocument>;
    docChange: DocChange;
  }>();

  const childQuestions = useMemo(() => {
    const questions = question?.properties?.[childQuestionsAccessor] || [];
    return questions;
  }, [question, childQuestionsAccessor]);

  const shouldShowAddChartButton = useMemo(() => {
    return showAddChartButton && childQuestions.length === 0;
  }, [showAddChartButton, childQuestions.length]);

  const shouldShowVisibility = useMemo(() => {
    return showAddChartButton && childQuestions.length > 0;
  }, [showAddChartButton, childQuestions.length]);

  const chartOptions = useMemo(() => {
    return childQuestions.map(q => ({
      value: q.id,
      label: q.text
    }));
  }, [childQuestions]);

  const defaultViewOptions = Object.values(TableDefaultView).map(value => ({
    value,
    label: value.charAt(0).toUpperCase() + value.slice(1) //TODO: USE dictionary
  }));

  const { updateQuestionHash, questionPath } = useContext(
    ConfigurationFormContext
  );

  const { autogenerateIdentifier } = useCheckQuestionDuplicateIdentifier();

  const handleSelectChart = useCallback(
    (path: FormPath) => () => {
      const newPath = path.join(".");
      if (questionPath === newPath) {
        updateQuestionHash("none");
      } else {
        updateQuestionHash(newPath);
      }
    },
    [questionPath, updateQuestionHash]
  );

  const addChart = useCallback(
    (type: ChartType = ChartType.LINE) => {
      const q = createChartQuestion(type);
      q.identifier = autogenerateIdentifier(q, q.text, question);
      docChange(d => {
        const question = getByPath<MultiLevelQuestion>(d, path);
        if (!question) {
          console.error("Question not found", path);
          return;
        }
        if (!question.properties) {
          question.properties = { [childQuestionsAccessor]: [] };
        }
        question.properties[childQuestionsAccessor] ??= [];
        question.properties[childQuestionsAccessor].push(q);
      });
      return q.id;
    },
    [docChange, path, childQuestionsAccessor, autogenerateIdentifier, question]
  );

  const removeChart = useCallback(
    (id: string) => {
      docChange(d => {
        const question = getByPath<MultiLevelQuestion>(d, path);
        if (!question) {
          console.error("Question not found", path, id);
          return;
        }
        if (
          !question.properties &&
          !question.properties[childQuestionsAccessor]
        ) {
          return;
        }
        const index =
          question.properties[childQuestionsAccessor]?.findIndex(
            q => q.id === id
          ) ?? -1;
        if (index != -1) {
          question.properties[childQuestionsAccessor]?.splice(index, 1);
        }
      });
    },
    [docChange, path, childQuestionsAccessor]
  );

  const reorderCharts: OnDrop = ({ source, destination }) => {
    const reordered = reorderWithEdge({
      list: childQuestions,
      startIndex: source.index,
      indexOfTarget: destination.index,
      closestEdgeOfTarget: destination.closestEdgeOfTarget,
      axis: "vertical"
    });

    docChange(d => {
      const question = getByPath<MultiLevelQuestion>(d, path);
      if (!question) {
        console.error("Question not found", path);
        return;
      }

      if (
        !question.properties &&
        !question.properties[childQuestionsAccessor]
      ) {
        return;
      }

      question.properties[childQuestionsAccessor] = JSON.parse(
        JSON.stringify(reordered)
      );
    });
  };

  const accordionTrigger: CustomAccordionTrigger = useCallback(
    ({ isOpen, onClick }) => (
      <Inline
        width="100"
        onClick={onClick}
        style={{
          cursor: "pointer",
          marginTop: "var(--spacing-050)"
        }}
        alignment="left"
        gap="050"
      >
        <Inline alignment="left" gap="000">
          <Heading size={HeadingSize.XXS} color={ColorText.SECONDARY}>
            {d(`ui.configuration.forms.question.chart.title`)}
          </Heading>
        </Inline>
        <Inline alignment="left">
          {<Pill label={`${childQuestions?.length}`} />}
          <OpenCloseIcon isOpen={isOpen} />
        </Inline>
      </Inline>
    ),
    [childQuestions?.length, d]
  );

  return (
    <Stack gap="050" className="chart-properties">
      {shouldShowAddChartButton && (
        <Stack width="100" alignment="left" gap="100">
          <SplitButton
            label={d(`ui.configuration.forms.question.chart.add`)}
            isOpen={isOpen}
            onOpenChange={setIsOpen}
            onClick={onClickWithClose(addChart)}
            size="default"
            variant="secondary"
            leftIcon={{ name: "add" }}
          >
            {ChartQuestionTypes.map((type: ChartType) => (
              <DropdownItem
                id={type}
                key={`chart-${type}`}
                onClick={() => {
                  addChart(type);
                  setIsOpen(false);
                }}
                description={d(
                  `ui.configuration.forms.question.type.chart.type.${type}.description`
                )}
              >
                <QuestionType type={type} size="regular" />
              </DropdownItem>
            ))}
          </SplitButton>
        </Stack>
      )}
      {!shouldShowAddChartButton && (
        <Accordion
          contentOverflow="visible"
          trigger={accordionTrigger}
          isOpen={isExpanded}
          onOpenChange={onChangeExpanded}
        >
          <Stack gap="100" style={{ paddingLeft: "var(--spacing-000)" }}>
            <ChartQuestions
              questions={childQuestions}
              removeQuestion={removeChart}
              onClick={handleSelectChart}
              onDrop={reorderCharts}
              path={[...path, "properties", childQuestionsAccessor]}
              selectedQuestionPath={questionPath}
              showReorder={showReorder}
              parentQuestion={question}
              mode={mode}
            />
            {mode === ConfigurationFormMode.EDIT && (
              <ConfigurationFormAddLine
                questionText={d(`ui.configuration.forms.question.chart.add`)}
                addChart={questionType => {
                  const id = addChart(questionType);
                  handleSelectChart([
                    ...path,
                    "properties",
                    childQuestionsAccessor,
                    id
                  ])();
                }}
              />
            )}
          </Stack>
        </Accordion>
      )}
      {shouldShowVisibility && (
        <Stack gap="150">
          <LabelledDivider label="Visibility" />
          <RadioGroup
            label="Default View"
            value={question?.properties?.defaultView}
            options={defaultViewOptions}
            onChange={value => {
              docChange(d => {
                const q = getByPath<Question>(
                  d,
                  path
                ) as Question<TableQuestionProperties>;
                if (!q || !q.properties) {
                  console.error("Question not found", path);
                  return;
                }
                q.properties.defaultView = value as TableDefaultView;
              });
            }}
            allowClear={true}
          />

          <MultiSelect
            label="Select graph(s) to display"
            required={false}
            options={chartOptions}
            value={question?.properties?.displayedGraphs || []}
            onChange={value => {
              docChange(d => {
                const q = getByPath<Question>(
                  d,
                  path
                ) as Question<TableQuestionProperties>;
                if (!q || !q.properties) {
                  console.error("Question not found", path);
                  return;
                }
                q.properties.displayedGraphs = value as string[];
              });
            }}
          />
        </Stack>
      )}
    </Stack>
  );
};

ChartProperties.displayName = "ChartProperties";
