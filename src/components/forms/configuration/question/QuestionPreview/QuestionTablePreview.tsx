// QuestionTablePreview.tsx
import React, { useState } from "react";

import { Stack, TabGroup } from "@oneteam/onetheme";

import { QuestionAnswer } from "@pages/collection/forms/questions/CollectionQuestionBlock/QuestionAnswer";

import { Question } from "@src/types/Question";
import {
  TableDefaultView,
  TableQuestionProperties
} from "@src/types/QuestionProperties";

export const QuestionTablePreview = ({
  question,
  parentQuestion
}: {
  question: Question<TableQuestionProperties>;
  parentQuestion?: Question<TableQuestionProperties>;
}) => {
  console.log("Table question detected", question, parentQuestion);
  const [tablePreviewPanelTab, setTablePreviewPanelTab] = useState<string>(
    question?.properties?.defaultView ?? TableDefaultView.ALL
  );

  const defaultViewOptions = Object.values(TableDefaultView).map(value => ({
    value,
    label: value.charAt(0).toUpperCase() + value.slice(1) //TODO: USE dictionary
  }));
  return (
    <Stack className="question-table-preview__container" gap="150">
      <div className="question-table-preview__header">
        <TabGroup
          options={defaultViewOptions}
          value={tablePreviewPanelTab}
          handleChange={setTablePreviewPanelTab}
          className="question-table-preview__tabs"
        />
      </div>

      {tablePreviewPanelTab === TableDefaultView.ALL && (
        <>
          {(question?.properties as TableQuestionProperties)?.charts?.map(
            chart => (
              <QuestionAnswer
                key={chart.id}
                question={chart}
                answer={undefined}
                answerAccessor={""}
                parentQuestion={question ?? undefined}
              />
            )
          )}
          <QuestionAnswer
            question={question}
            answer={undefined}
            answerAccessor={""}
            parentQuestion={parentQuestion ?? undefined}
          />
        </>
      )}
      {tablePreviewPanelTab === TableDefaultView.GRAPH && (
        <>
          {(question?.properties as TableQuestionProperties)?.charts?.map(
            chart => (
              <QuestionAnswer
                key={chart.id}
                question={chart}
                answer={undefined}
                answerAccessor={""}
                parentQuestion={question ?? undefined}
              />
            )
          )}
        </>
      )}
      {tablePreviewPanelTab === TableDefaultView.TABLE && (
        <QuestionAnswer
          question={question}
          answer={undefined}
          answerAccessor={""}
          parentQuestion={parentQuestion ?? undefined}
        />
      )}
    </Stack>
  );
};
